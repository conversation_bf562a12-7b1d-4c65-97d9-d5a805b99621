# 需求分析

## 系统总体架构

系统采用模块化设计，分为5大核心功能模块，服务于建筑幕墙行业的不同参与方：

1. **协会管理系统** - 面向行业协会管理人员，提供会员、资质、信用等核心管理功能
2. **企业管理系统** - 服务幕墙企业，涵盖组织、项目、供需等业务管理
3. **从业人员系统** - 为施工/管理人员提供职业发展平台
4. **消费者服务系统** - 连接终端用户与服务提供商
5. **综合门户站点** - 统一入口和行业信息枢纽

各模块既独立运行又通过统一用户中心和数据平台实现互联互通。


## 协会管理系统

### 会员管理
### 会员全生命周期管理
**入会管理**：标准化入会流程（申请→审核→建档）确保会员质量
**会籍管理**：主会/分会管理架构设计
**分级体系**：基于会员等级的动态权限分配机制

**状态管理设计**：
- **未入会**：了解没有加入协会的企业，可以拓展会员。建立潜在会员库，支持定向营销和入会引导
- **已入会**：活跃会员服务与管理
- **续费**：自动化续费提醒和流程
- **离会**：会员历史数据归档机制

### 资质认定体系

以评审标准和认定方法的制定为依据，对不同的评审认定情况进行认定评审工作；
可采用资质预审核+专家评审双机制进行审核，使用审批流程即可以灵活的进行配制管理审核认定办法。

认定的参与对象有，比如：

#### 检查组申请
#### 检查组人员申请

#### 企业认定申请
#### 管理人员申请
#### 施工人员申请


### 信用治理体系

**数据架构**：
- 多源数据整合：政府数据（行政处罚）、企业数据（合同履行）、人员数据（劳务记录）
- 信用评价模型：加权评分算法

**应用场景**：
- **协会督察**：行业自律监管机制
- **政-企信用**：政府数据整合应用
  - 行政处罚：违规记录追踪
- **企-企信用**：商业合作评估
  - 合同纠纷：履约能力分析
- **从业人员-企业信用**：劳务纠纷记录
- **纠纷调解/仲裁**：在线争议解决平台
- **履约历史追踪**：全流程电子存证与公示
**信用数据源**：政府数据（行政处罚、资质信息）、企业间数据（合同纠纷）、人员数据（劳务纠纷）
**信用应用**：纠纷调解仲裁、信用评级与公示、履约历史追踪

#### **信用主体视角：以“制度约束”构建可信画像**
##### **1. 信用主体制度设计原则**
   - **全量归集**：通过法律法规明确信用主体必须提供的数据范围（如企业年报、个人社保记录），强制消除“数据黑箱”。  
   - **正向激励**：建立信用修复机制，允许主体通过履约整改、公益行为（如环保投入）提升信用等级，避免“一票否决”僵化。  
   - **权责对等**：信用主体的数据贡献度与其权益挂钩，例如数据共享越多的企业可享受更低利率贷款（如浙江“信用+金融”模式）。  

##### **2. 主体数据治理关键动作**  
   - **信息分层管理**：  
     - **基础层**：法定公开信息（如企业注册信息、司法判决），强制接入系统；  
     - **扩展层**：自愿补充信息（如供应链合作评价、环保认证），通过接口开放上传。  
   - **动态纠偏机制**：  
     - 设置异议申诉通道（如线上提交证据链），对错误数据实现48小时内标记冻结；  
---

#### **二、信用受体视角：以“案例匹配”驱动需求响应**
##### **1. 受体需求拆解逻辑**  
   - **场景分类**：  
     - **合作类需求**：侧重供应链稳定性案例（如交货准时率）、纠纷解决案例（诉讼调解成功率）。  
   - **需求颗粒度**：  
     - **粗粒度**：行业通用标签（如“制造业AAA级”）；  
     - **细粒度**：特定场景标签（如“投诉率<5%”）。  

##### **2. 案例库构建与检索机制**  

##### **3. 匹配算法优化路径**  
   - **初筛层**：基于规则引擎过滤基础条件（如注册年限≥3年、无重大违法记录）；  
   - **精筛层**：使用协同过滤算法，根据受体历史选择偏好推荐相似案例；  
   - **解释层**：生成可视化报告，标注案例匹配度得分及核心依据（如“选取该案例因行业匹配度达92%”）。  

---

#### **三、双向协同：构建“制度-需求”闭环生态**
##### **1. 数据流动机制**  
   - **正向反馈环**：  
     信用受体使用案例后需反馈效果，反向优化信用主体评分模型；  
   - **动态权重调整**：  
     根据受体检索热点，提升相关案例的推荐优先级。  



### 行业服务
**标准体系管理**：
- **资质列表**：详细记录各类资质种类，明确适用企业类型和人群，提供资质发展规划建议
- **证书列表**：系统整理证书体系，说明各类证书适用场景和获取路径，指导企业和个人证书规划  
- **职称列表**：完整职称体系展示，包含职称等级、评审条件和职业发展路径说明
- 三库联动：资质/证书/职称数据互通
- 智能匹配：基于企业/个人特征的推荐引擎

**标准/规范/政策法规**: 展示通告等

**增值服务**：
- 质检保障：第三方检测对接
- 资金托管：担保交易模式
- 培训体系：线上线下结合


## 企业系统

### 核心功能模块

1. **组织架构**：多级部门+岗位权限设计
2. **项目管理**：
   - 施工日志：移动端实时记录
   - 文档管理：合同/证书电子归档
   - 检查组项目立项 
   - 检查组成员邀请

3. **供需平台**：
   - 智能匹配：基于信用+能力的推荐算法
   - 交易跟踪：全流程交易履约跟踪状态监控

4. **资源库**：
   - 人才库：技能标签体系
   - 物料管理：分级权限控制


## 从业人员系统

### 职业发展平台

1. **档案管理**：
   - 简历：公开/保密双模式
   - 资质：电子证书验证
   - **申请、认定提交** 比如提交幕墙检查组成员信息填报

2. **职业发展通道**：
   - 技能认证：阶梯式培训考核课程助力成长体系
   - 信用积累：可视化成长轨迹
   - 岗位推荐：智能人岗匹配


## 消费者服务系统

### 服务流程设计

1. **需求发布**：标准化需求模板可以挑选使用
2. **服务匹配**：
   - 多维筛选条件
   - 服务提供商信用优先推荐
3. **售后保障**：
   - 评价体系
   - 投诉处理流程


## 综合门户站点

### 统一登录入口
#### 协会
#### 企业
#### 从业人员
#### 消费者

### 资源检索服务
#### 找需求
#### 找材料
#### 找企业

### 行业动态
#### 展示通报
#### 明星企业
#### 标杆项目
#### 优秀专家
#### 听课学习、培训报名
#### 我要赞助、投广告

## 实施要点
1. 建立统一用户中心，实现单点登录
2. 设计可扩展的权限管理体系
3. 开发信用数据交换平台
4. 构建智能推荐引擎
5. 确保移动端兼容性（小程序/公众号）
